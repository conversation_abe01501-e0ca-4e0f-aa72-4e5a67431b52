const crypto = require('crypto');

// 配置
const SECRET = "fc8c7ef845baff7935591112465173e7";
const sessionData = {"username": "admin"};

// 方法1: 使用HMAC签名 (类似于你提供的例子)
function createSignedSession(data, secret) {
    // 将数据转换为JSON字符串并Base64编码
    const sessionValue = Buffer.from(JSON.stringify(data)).toString('base64');
    
    // 创建HMAC签名
    const hmac = crypto.createHmac('sha256', secret);
    hmac.update(sessionValue);
    const signature = hmac.digest('base64url'); // 使用base64url编码，更适合URL
    
    return {
        session: sessionValue,
        'session.sig': signature
    };
}

// 验证签名
function verifySignedSession(sessionValue, signature, secret) {
    const hmac = crypto.createHmac('sha256', secret);
    hmac.update(sessionValue);
    const expectedSignature = hmac.digest('base64url');
    
    return signature === expectedSignature;
}

// 方法2: 使用AES加密
function createEncryptedSession(data, secret) {
    // 从secret生成密钥
    const key = crypto.scryptSync(secret, 'salt', 32);
    const iv = crypto.randomBytes(16);
    
    // 加密数据
    const cipher = crypto.createCipher('aes-256-cbc', key);
    let encrypted = cipher.update(JSON.stringify(data), 'utf8', 'hex');
    encrypted += cipher.final('hex');
    
    // 将IV和加密数据组合
    const encryptedData = iv.toString('hex') + ':' + encrypted;
    
    return {
        session: Buffer.from(encryptedData).toString('base64')
    };
}

// 解密会话
function decryptSession(encryptedSession, secret) {
    try {
        // 解码Base64
        const encryptedData = Buffer.from(encryptedSession, 'base64').toString('utf8');
        const [ivHex, encrypted] = encryptedData.split(':');
        
        // 从secret生成密钥
        const key = crypto.scryptSync(secret, 'salt', 32);
        const iv = Buffer.from(ivHex, 'hex');
        
        // 解密
        const decipher = crypto.createDecipher('aes-256-cbc', key);
        let decrypted = decipher.update(encrypted, 'hex', 'utf8');
        decrypted += decipher.final('utf8');
        
        return JSON.parse(decrypted);
    } catch (error) {
        console.error('解密失败:', error.message);
        return null;
    }
}

// 方法3: JWT风格的签名 (更现代的方式)
function createJWTStyleSession(data, secret) {
    // 创建header
    const header = {
        "alg": "HS256",
        "typ": "JWT"
    };
    
    // Base64URL编码
    const encodedHeader = Buffer.from(JSON.stringify(header)).toString('base64url');
    const encodedPayload = Buffer.from(JSON.stringify(data)).toString('base64url');
    
    // 创建签名
    const signingInput = `${encodedHeader}.${encodedPayload}`;
    const hmac = crypto.createHmac('sha256', secret);
    hmac.update(signingInput);
    const signature = hmac.digest('base64url');
    
    return `${encodedHeader}.${encodedPayload}.${signature}`;
}

// 验证JWT风格的token
function verifyJWTStyleSession(token, secret) {
    try {
        const [header, payload, signature] = token.split('.');
        
        // 验证签名
        const signingInput = `${header}.${payload}`;
        const hmac = crypto.createHmac('sha256', secret);
        hmac.update(signingInput);
        const expectedSignature = hmac.digest('base64url');
        
        if (signature !== expectedSignature) {
            return null;
        }
        
        // 解码payload
        const decodedPayload = Buffer.from(payload, 'base64url').toString('utf8');
        return JSON.parse(decodedPayload);
    } catch (error) {
        console.error('JWT验证失败:', error.message);
        return null;
    }
}

// 执行示例
console.log('=== 原始数据 ===');
console.log(JSON.stringify(sessionData));

console.log('\n=== 方法1: HMAC签名 (类似你的例子) ===');
const signedSession = createSignedSession(sessionData, SECRET);
console.log('Cookie格式:');
console.log(`session.sig=${signedSession['session.sig']}; session=${signedSession.session}`);
console.log('\n验证结果:', verifySignedSession(signedSession.session, signedSession['session.sig'], SECRET));

console.log('\n=== 方法2: AES加密 ===');
const encryptedSession = createEncryptedSession(sessionData, SECRET);
console.log('加密后的session:', encryptedSession.session);
console.log('解密结果:', decryptSession(encryptedSession.session, SECRET));

console.log('\n=== 方法3: JWT风格 ===');
const jwtToken = createJWTStyleSession(sessionData, SECRET);
console.log('JWT Token:', jwtToken);
console.log('验证结果:', verifyJWTStyleSession(jwtToken, SECRET));

// 解码你提供的例子
console.log('\n=== 解码你提供的例子 ===');
const originalSession = "eyJ1c2VybmFtZSI6IjEyMyJ9";
const decodedOriginal = Buffer.from(originalSession, 'base64').toString('utf8');
console.log('原始session解码:', decodedOriginal);
