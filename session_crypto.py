import json
import base64
import hmac
import hashlib
from cryptography.fernet import <PERSON><PERSON><PERSON>
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
import os

# 配置
SECRET = "fc8c7ef845baff7935591112465173e7"
session_data = {"username": "admin"}

def create_signed_session(data, secret):
    """方法1: 使用HMAC签名 (类似于你提供的例子)"""
    # 将数据转换为JSON字符串并Base64编码
    session_value = base64.b64encode(json.dumps(data).encode()).decode()
    
    # 创建HMAC签名
    signature = hmac.new(
        secret.encode(),
        session_value.encode(),
        hashlib.sha256
    ).digest()
    
    # 使用base64编码签名
    signature_b64 = base64.b64encode(signature).decode().rstrip('=')
    
    return {
        'session': session_value,
        'session.sig': signature_b64
    }

def verify_signed_session(session_value, signature, secret):
    """验证HMAC签名"""
    expected_signature = hmac.new(
        secret.encode(),
        session_value.encode(),
        hashlib.sha256
    ).digest()
    
    expected_signature_b64 = base64.b64encode(expected_signature).decode().rstrip('=')
    return signature == expected_signature_b64

def create_encrypted_session(data, secret):
    """方法2: 使用Fernet加密"""
    # 从secret生成密钥
    kdf = PBKDF2HMAC(
        algorithm=hashes.SHA256(),
        length=32,
        salt=b'salt_',  # 在实际应用中应该使用随机salt
        iterations=100000,
    )
    key = base64.urlsafe_b64encode(kdf.derive(secret.encode()))
    
    # 创建Fernet实例并加密
    f = Fernet(key)
    encrypted_data = f.encrypt(json.dumps(data).encode())
    
    return {
        'session': base64.b64encode(encrypted_data).decode()
    }

def decrypt_session(encrypted_session, secret):
    """解密会话"""
    try:
        # 从secret生成密钥
        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=32,
            salt=b'salt_',
            iterations=100000,
        )
        key = base64.urlsafe_b64encode(kdf.derive(secret.encode()))
        
        # 解密
        f = Fernet(key)
        encrypted_data = base64.b64decode(encrypted_session.encode())
        decrypted_data = f.decrypt(encrypted_data)
        
        return json.loads(decrypted_data.decode())
    except Exception as e:
        print(f'解密失败: {e}')
        return None

def create_jwt_style_session(data, secret):
    """方法3: JWT风格的签名"""
    # 创建header
    header = {
        "alg": "HS256",
        "typ": "JWT"
    }
    
    # Base64URL编码
    encoded_header = base64.urlsafe_b64encode(json.dumps(header).encode()).decode().rstrip('=')
    encoded_payload = base64.urlsafe_b64encode(json.dumps(data).encode()).decode().rstrip('=')
    
    # 创建签名
    signing_input = f"{encoded_header}.{encoded_payload}"
    signature = hmac.new(
        secret.encode(),
        signing_input.encode(),
        hashlib.sha256
    ).digest()
    
    encoded_signature = base64.urlsafe_b64encode(signature).decode().rstrip('=')
    
    return f"{encoded_header}.{encoded_payload}.{encoded_signature}"

def verify_jwt_style_session(token, secret):
    """验证JWT风格的token"""
    try:
        header, payload, signature = token.split('.')
        
        # 验证签名
        signing_input = f"{header}.{payload}"
        expected_signature = hmac.new(
            secret.encode(),
            signing_input.encode(),
            hashlib.sha256
        ).digest()
        
        expected_signature_b64 = base64.urlsafe_b64encode(expected_signature).decode().rstrip('=')
        
        if signature != expected_signature_b64:
            return None
        
        # 解码payload
        # 添加padding如果需要
        payload += '=' * (4 - len(payload) % 4)
        decoded_payload = base64.urlsafe_b64decode(payload.encode()).decode()
        return json.loads(decoded_payload)
    except Exception as e:
        print(f'JWT验证失败: {e}')
        return None

# 执行示例
if __name__ == "__main__":
    print('=== 原始数据 ===')
    print(json.dumps(session_data))

    print('\n=== 方法1: HMAC签名 (类似你的例子) ===')
    signed_session = create_signed_session(session_data, SECRET)
    print('Cookie格式:')
    print(f"session.sig={signed_session['session.sig']}; session={signed_session['session']}")
    print(f'\n验证结果: {verify_signed_session(signed_session["session"], signed_session["session.sig"], SECRET)}')

    print('\n=== 方法3: JWT风格 ===')
    jwt_token = create_jwt_style_session(session_data, SECRET)
    print(f'JWT Token: {jwt_token}')
    print(f'验证结果: {verify_jwt_style_session(jwt_token, SECRET)}')

    # 解码你提供的例子
    print('\n=== 解码你提供的例子 ===')
    original_session = "eyJ1c2VybmFtZSI6IjEyMyJ9"
    decoded_original = base64.b64decode(original_session.encode()).decode()
    print(f'原始session解码: {decoded_original}')
    
    # 为你的例子创建正确的签名
    print('\n=== 为原始例子创建签名 ===')
    original_signature = hmac.new(
        SECRET.encode(),
        original_session.encode(),
        hashlib.sha256
    ).digest()
    original_signature_b64 = base64.b64encode(original_signature).decode().rstrip('=')
    print(f'原始数据的正确签名: {original_signature_b64}')
